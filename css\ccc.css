#{
	padding: 0;
	margin: 0;
}
a{
	list-style: none;
}
body{
	background-color: #fff;
}
ul {
    list-style: none;
}

a {
    color: inherit;
    text-decoration: none;
}
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

.left{
    float: left;
}

.right{
    float: right;
}
.clear{
    clear: both;
}
.clearboth::after {
    content: '';
    clear: both;
    display: block;
    width: 0;
    height: 0;
    overflow: hidden;
}

.flexbox {
    display: -webkit-flex;
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: flex;
}

.home-title{
    margin: 20px 0 30px; /* 调整标题与内容的间距 */
    text-align: center;
}

.home-title h2 {
    position: relative;
    display: inline-block;
    padding-bottom: 10px;
}

.home-title h2:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background-color: #09d2fe;
}

.home-title h3{
    padding-left: 10px;
    border-left: 8px solid #0281E8;
    text-align: left;
}

.home-text p{
    text-indent: 2em;
    line-height: 30px;
}

.home-img{
    overflow: hidden;
}

.home-img img{
    vertical-align: top;
}

.current-nav{
    color: #fff !important;
    font-size: 20px !important;
    background-color: #09d2fe !important;
    border-bottom: 2px solid #ffee34 !important;
}

#header{
    position: relative; /* 改为相对定位，而不是粘性定位 */
    z-index: 999;
    width: 100%;
    /* background-color: #fff; */
    box-shadow: 0 8px 8px rgba(0, 0, 0, 0.087);
}

.top{
    background-color: #09d2fe;
}
.loginbar li{
    float: right;
    height: 35px;
    font-size: 14px;
    color: #fff;
    line-height: 35px;
}

.loginbar li:first-child{
    float: left;
    font-weight: 900;
    font-size: 20px;
    font-family: 'Times New Roman', Times, serif;
}

.loginbar li:first-child span{
    color: #ffee34;
}

.loginbar li a:hover{
    text-decoration: underline;
}

.loginbar li:first-child a:hover{
    text-decoration: none;
}

.banner-wrapper {
    position: relative;
    height: 300px; /* 减小高度 */
    color: #fff;
    background: url(../images/banner1.jpg) no-repeat center/cover;
    margin-bottom: 30px; /* 添加下边距 */
}

.child-banner {
    position: relative;
    height: 200px; /* 减小高度 */
    background: url(../images/banner1.jpg) no-repeat center/cover;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 30px; /* 添加下边距 */
}

.child-banner h2 {
    color: #fff;
    font-size: 32px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.5);
}

.nav {
    background-color: #fff;
}

.nav li{
    width: 14.2%;
    height: 60px;
    float: left;
    text-align: center;
}

.nav li a {
    display: inline-block;
    width: 100%;
    height: 100%;
    font-size: 18px;
    line-height: 60px;
}

.nav li a:hover {
   text-decoration: underline;
   color: #fff;
   background-color:#09d2fe;
}
.home-one-content{
    color: #fff;
    background-color: #57E0FF;
    background: url(../images/bg.png) no-repeat center bottom/cover;
    background-attachment: fixed;
}

img {
    max-width: 100%;
    height: auto;
    vertical-align: middle;
}

.cilumn-one-img img{
    width: 100%;
}

.home-one-img {
    width: 40%;
}

.home-one-main.flexbox {
    align-items: center;
    margin-bottom: 40px; /* 添加下边距 */
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    padding: 20px;
    backdrop-filter: blur(5px);
}

.home-one-text {
    padding: 0 40px;
    width: 60%;
}

.home-two-content{
    width: 18%; /* Adjust width for better spacing */
    height: 180px;
    margin-right: 24px;
    text-align: center;
    /* box-shadow: 0 0 8px rgba(0, 0, 0, 0.153); */
    transition: all .3s linear;
    color: #fff;
}

.home-two-content:last-child{
    margin: 0;
}

.home-two-content:hover {
    transform: translateY(-10px);
}

.home-two-content h2{
    padding-top: 50px;
    font-size: 42px;
    color: #fff;
    transition: all .3s linear;
}

.home-two-content h3{
    color: #fff;
    margin-bottom: 5px;
}

.home-two-content p{
    display: none;
    padding: 5px;
    color: #fff;
}

.atlas-list ul {
    margin: 40px 0;
}

.atlas-list li {
    float: left;
    width: 32%;
    margin-right: 2%;
    margin-bottom: 20px;
    box-shadow: 0 0 8px rgba(0,0,0,0.1);
    transition: all .3s linear;
}

.atlas-list li:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

.atlas-list li:nth-child(3n) {
    margin-right: 0;
}

.header img {
    width: 100%;
    display: block;
}

.home-two {
    margin: 40px 0; /* 添加上下边距 */
}

.contactus-banner {
    text-align: center;
    padding: 40px 0;
}

.contactus-form {
    max-width: 600px;
    margin: 0 auto 40px;
    padding: 30px;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    backdrop-filter: blur(5px);
}

.email-box {
    max-width: 600px;
    margin: 0 auto 30px;
    padding: 20px;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    backdrop-filter: blur(5px);
}

.email-box li {
    padding: 10px 0;
    border-bottom: 1px dashed rgba(255, 255, 255, 0.3);
}

.email-box li:last-child {
    border-bottom: none;
}

.email-box li a {
    color: #09d2fe;
    transition: all 0.3s ease;
}

.email-box li a:hover {
    color: #fff;
    text-decoration: underline;
}

.contactus-content {
    padding: 30px;
    margin-bottom: 40px;
}

.leave-message input[type="text"],
.leave-message input[type="tel"],
.leave-message input[type="email"],
.leave-message textarea {
    width: 100%;
    padding: 10px;
    margin: 5px 0 15px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 5px;
    background-color: rgba(255, 255, 255, 0.2);
    color: #333;
}

.leave-message textarea {
    height: 150px;
    resize: vertical;
}

.leave-message input[type="submit"],
.leave-message input[type="reset"] {
    padding: 10px 20px;
    margin-right: 10px;
    border: none;
    border-radius: 5px;
    background-color: #09d2fe;
    color: #fff;
    cursor: pointer;
    transition: all 0.3s ease;
}

.leave-message input[type="submit"]:hover,
.leave-message input[type="reset"]:hover {
    background-color: #0281E8;
}

.userbar {
    display: flex;
    justify-content: space-between;
}

.userbar > div {
    width: 48%;
}

.atlas-section {
    margin-bottom: 50px;
    padding: 30px;
    border-radius: 15px;
}

.atlas-section .home-title {
    margin-top: 0;
}

.desktop-wallpapers li {
    width: 32%; /* 电脑壁纸每行显示3个 */
}

.mobile-wallpapers {
    display: flex;
    justify-content: center;
}

.mobile-wallpapers ul {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    max-width: 900px;
}

.mobile-wallpapers li {
    width: 22%; /* 手机壁纸每行显示4个 */
    margin: 0 1.5% 20px;
}

.atlas-list li {
    overflow: hidden;
    border-radius: 10px;
    transition: all 0.3s ease;
}

.atlas-list li:hover {
    transform: translateY(-10px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.atlas-list img {
    transition: all 0.5s ease;
}

.atlas-list li:hover img {
    transform: scale(1.05);
}

/* Responsive Styles */
@media (max-width: 1024px) {
    .home-one-main.flexbox {
        flex-direction: column;
    }

    .home-one-img, .home-one-text {
        width: 100%;
        padding: 20px 0;
    }

    .home-two .flexbox {
        flex-wrap: wrap;
        justify-content: center;
    }

    .home-two-content {
        width: 45%;
        margin-bottom: 20px;
    }
}

@media (max-width: 768px) {
    .nav li {
        width: 50%;
    }

    .home-two-content {
        width: 100%;
    }

    .atlas-list li {
        width: 100%;
    }
}

@media (max-width: 768px) {
    .desktop-wallpapers li,
    .mobile-wallpapers li {
        width: 48%; /* 在平板上每行显示2个 */
        margin: 0 1% 20px;
    }
}

@media (max-width: 480px) {
    .desktop-wallpapers li,
    .mobile-wallpapers li {
        width: 100%; /* 在手机上每行显示1个 */
        margin: 0 0 20px;
    }
}

/* 登录和注册页面样式 */
.login-container,
.register-container {
    max-width: 500px;
    margin: 0 auto 40px;
    padding: 30px;
    border-radius: 15px;
}

.login-form h2,
.register-form h2 {
    text-align: center;
    margin-bottom: 30px;
    color: #333;
    position: relative;
}

.login-form h2:after,
.register-form h2:after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background-color: #09d2fe;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
}

.form-group input[type="text"],
.form-group input[type="password"],
.form-group input[type="email"] {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 5px;
    background-color: rgba(255, 255, 255, 0.2);
    color: #333;
    font-size: 16px;
    transition: all 0.3s ease;
}

.form-group input:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(9, 210, 254, 0.3);
    background-color: rgba(255, 255, 255, 0.4);
}

.remember-me,
.agreement {
    display: flex;
    align-items: center;
}

.remember-me input,
.agreement input {
    margin-right: 10px;
}

.form-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 30px;
}

.btn-submit,
.btn-reset {
    padding: 12px 25px;
    border: none;
    border-radius: 5px;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-submit {
    background-color: #09d2fe;
    color: #fff;
}

.btn-reset {
    background-color: rgba(255, 255, 255, 0.3);
    color: #333;
}

.btn-submit:hover {
    background-color: #0281E8;
}

.btn-reset:hover {
    background-color: rgba(255, 255, 255, 0.5);
}

.register-link,
.login-link {
    text-align: center;
    margin-top: 20px;
}

.register-link a,
.login-link a,
.agreement a {
    color: #09d2fe;
    text-decoration: none;
    transition: all 0.3s ease;
}

.register-link a:hover,
.login-link a:hover,
.agreement a:hover {
    color: #0281E8;
    text-decoration: underline;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .login-container,
    .register-container {
        max-width: 100%;
        padding: 20px;
    }
    
    .form-actions {
        flex-direction: column;
        gap: 15px;
    }
    
    .btn-submit,
    .btn-reset {
        width: 100%;
    }
}
