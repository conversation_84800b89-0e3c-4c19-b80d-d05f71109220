/* css/theme.css */
body {
    background-image: url('../images/9c41896dea6f3969e295088b97a6aeb4.jpeg');
    background-size: cover;
    background-attachment: fixed;
    margin: 0;
    padding: 0;
}

:root {
    --glass-blur: 10px;
    --glass-bg-color: rgba(255, 255, 255, 0.5);
}

.glassmorphism {
    background: var(--glass-bg-color);
    backdrop-filter: blur(var(--glass-blur));
    -webkit-backdrop-filter: blur(var(--glass-blur));
    border-radius: 10px;
    border: 1px solid rgba(255, 255, 255, 0.18);
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

/* 修复导航栏样式 */
#header.glassmorphism {
    border-radius: 0;
    margin-bottom: 0;
}

.nav {
    background-color: transparent;
}

/* 确保内容不被导航栏遮挡 */
.container {
    position: relative;
    z-index: 1;
} 